{"name": "react-login-signup-system", "version": "0.0.5", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@headlessui/react": "^2.2.4", "@metamask/detect-provider": "^2.0.0", "@metamask/logo": "^4.0.0", "@mui/material": "^7.3.1", "@redux-devtools/extension": "^3.3.0", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.2", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "chalk": "^5.5.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "eslint": "^8.57.1", "ethers": "^6.15.0", "express": "^5.1.0", "firebase": "^12.1.0", "jest": "^27.5.1", "joi": "^18.0.0", "jsonwebtoken": "^9.0.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "mongoose": "^8.17.1", "morgan": "^1.10.1", "multer": "^2.0.2", "next": "^15.4.6", "nodemon": "^3.1.10", "pg": "^8.16.3", "prettier": "^3.6.2", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-redux": "^9.2.0", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "recharts": "^2.15.3", "redux-thunk": "^3.1.0", "socket.io": "^4.8.1", "supertest": "^7.1.4", "filigrean-icon": "^1.0.0", "ts-node": "^10.9.2", "uuid": "^11.1.0", "validator": "^13.15.15", "web-vitals": "^2.1.4", "web3": "^4.16.0", "winston": "^3.17.0", "yup": "^1.7.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "postinstall": "npm start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.2.4"}}